function cleanPhoneNumber(phoneNumber) {
    // Chỉ loại bỏ ký tự không phải số, giữ nguyên tất cả số 0 đầu
    return phoneNumber.replace(/[^0-9]/g, '');
}

function findCustomerInfoByPhoneNumber(phoneNumber) {
    var customer = null;

    // Clean phone number trước khi gửi API để đồng bộ với backend
    var cleanedPhoneNumber = cleanPhoneNumber(phoneNumber);

    $.ajax({
        url: SHOW_CUSTOMER_BY_PHONE_NUMBER_API_URL,
        type: 'GET',
        dataType: 'json',
        data: {
            'phone_number': cleanedPhoneNumber,
        },
        async: false,
        success: function (res) {
            customer = res.data;
            console.log('Customer found via API:', customer);
        },
        error: function(xhr, status, error) {
            // Debug: log lỗi để kiểm tra
            console.log('API Error:', {
                status: xhr.status,
                response: xhr.responseJSON,
                phoneNumber: cleanedPhoneNumber,
                url: SHOW_CUSTOMER_BY_PHONE_NUMBER_API_URL
            });
        }
    });

    return customer;
}

function showCustomerInfo(customer) {
    $('#customer_name').val(customer.name);
    $('#customer_age').val(customer.age);
    $('#customer_province_id').val(customer.province_id);
    $('#customer_address').val(customer.address);
}

function removeCustomerInfo() {
    $('#customer_name, #customer_age, #customer_province_id, #customer_address')
        .val(null);
}

function showOrRemoveCustomerInfo(phoneNumber) {
    // Clean phone number trước khi check length
    var cleanedPhoneNumber = cleanPhoneNumber(phoneNumber);

    console.log('Original phone:', phoneNumber, 'Cleaned phone:', cleanedPhoneNumber);
    console.log('Length check:', cleanedPhoneNumber.length, 'Min:', CUSTOMER_PHONE_NUMBER_MIN_LENGTH, 'Max:', CUSTOMER_PHONE_NUMBER_MAX_LENGTH);

    if (!cleanedPhoneNumber.length || cleanedPhoneNumber.length < CUSTOMER_PHONE_NUMBER_MIN_LENGTH || cleanedPhoneNumber.length > CUSTOMER_PHONE_NUMBER_MAX_LENGTH) {
        console.log('Phone number length invalid, removing customer info');
        return removeCustomerInfo();
    }

    // Show customer.
    console.log('Searching for customer with phone:', cleanedPhoneNumber);
    const customer = findCustomerInfoByPhoneNumber(cleanedPhoneNumber);

    if (!customer) {
        console.log('Customer not found, removing customer info');
        return removeCustomerInfo();
    }

    console.log('Customer found:', customer);
    showCustomerInfo(customer);
}

$(document).on('keyup', '#customer_phone_number', function () {
    var phoneNumber = $(this).val();
    console.log('Phone number input:', phoneNumber);
    showOrRemoveCustomerInfo(phoneNumber);
});

function showFormErrors(errors, form) {
    $.each(errors, function (attr, messages) {
        attr = attr.replace('.', '_');

        const formGroup = form.find('#' + attr).closest('.form-group');

        formGroup.addClass('has-error');
        formGroup.append(
            '<span class="help-block">' + messages.join('<br>') + '</span>'
        );
    });
}

function hideFormErrors(form) {
    form.find('.form-group').removeClass('has-error');
    form.find('.form-group').find('span.help-block').remove();
}

$(document).on('submit', 'form#js_form_create_edit_booking', function (e) {
    e.preventDefault();

    const form = $(this);
    $('#submit-booking').attr('disabled', true);

    hideFormErrors(form);

    $.ajax({
        url: form.attr('action'),
        type: form.attr('method'),
        data: form.serialize(),
        async: false,
        success: function (res) {
            window.location.href = res.data.redirect_url;
        },
        error: function (error) {
            $('#submit-booking').attr('disabled', false);

            // Debug logging
            console.log('=== BOOKING UPDATE ERROR ===');
            console.log('Status:', error.status);
            console.log('Response:', error.responseJSON);
            console.log('Full error:', error);

            if (error.status === 422) {
                console.log('Validation errors:', error.responseJSON.messages);
                showFormErrors(error.responseJSON.messages, form);

                // Hiển thị validation errors trong console
                if (error.responseJSON.messages) {
                    console.table(error.responseJSON.messages);
                }
            } else {
                alert('Đã có lỗi xảy ra. Status: ' + error.status);
                //window.location.reload();
            }
        }
    });
});
