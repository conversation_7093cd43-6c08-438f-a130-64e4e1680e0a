<?php

namespace App\Repositories\Eloquent;

use App\Contracts\Repositories\CustomerRepositoryInterface;
use App\Enums\Booking\CustomerComingResultEnum;
use App\Models\Customer;
use Illuminate\Contracts\Pagination\Paginator;

class CustomerRepository extends BaseRepository implements CustomerRepositoryInterface
{
    public function model(): string
    {
        return Customer::class;
    }

    public function getPaginator(int $perPage, array $conditions = []): Paginator
    {
        return $this->model
            ->newQuery()
            ->when(isset($conditions['keyword']) && strlen($conditions['keyword']), function ($builder) use ($conditions) {
                $builder->where(function ($builder) use ($conditions) {
                    $builder
                        ->orWhere('name', 'LIKE', "%{$conditions['keyword']}%")
                        ->orWhere('phone_number', 'LIKE', "%{$conditions['keyword']}%");
                });
            })
            ->when(!empty($conditions['has_revenue']), function ($builder) {
                $builder->whereHas('customerRevenues');
            })
            ->with([
                'firstNewBooking',
                'firstNewBooking.product',
                'firstNewBooking.bookingSources',
                'firstNewBooking.facebookPage',
                'customerRevenues',
            ])
            ->withCount([
                'bookings' => function ($builder) {
                    $builder->whereIn('customer_coming_result', CustomerComingResultEnum::getCameResults());
                },
            ])
            ->withCount([
                'bookings as bookings_failed_count' => function ($builder) {
                    $builder->where('customer_coming_result', CustomerComingResultEnum::CameWithFailed);
                },
            ])
            ->paginate($perPage);
    }

    public function findByPhoneNumber(string $phoneNumber): ?Customer
    {
        // Làm sạch phone number để tìm chính xác
        $cleanedPhoneNumber = $this->cleanPhoneNumber($phoneNumber);

        return $this->model
            ->newQuery()
            ->where('phone_number', $cleanedPhoneNumber)
            ->first();
    }

    public function existsCustomerByPhoneNumber(string $phoneNumber, $exceptId = null): bool
    {
        // Làm sạch phone number để check chính xác
        $cleanedPhoneNumber = $this->cleanPhoneNumber($phoneNumber);

        return $this
            ->model
            ->newQuery()
            ->when(isset($exceptId), function ($builder) use ($exceptId) {
                $builder->whereNotIn('id', (array)$exceptId);
            })
            ->where('phone_number', $cleanedPhoneNumber)
            ->exists();
    }

    /**
     * Làm sạch phone number - chỉ loại bỏ ký tự đặc biệt, giữ nguyên leading zeros
     * VD: "0824-083-812" -> "0824083812", "00824083812" -> "00824083812"
     */
    protected function cleanPhoneNumber(string $phoneNumber): string
    {
        // Chỉ loại bỏ ký tự không phải số, giữ nguyên tất cả số 0 đầu
        return preg_replace('/[^0-9]/', '', $phoneNumber);
    }
}
