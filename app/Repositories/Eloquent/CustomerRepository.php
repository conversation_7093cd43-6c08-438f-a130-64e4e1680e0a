<?php

namespace App\Repositories\Eloquent;

use App\Contracts\Repositories\CustomerRepositoryInterface;
use App\Enums\Booking\CustomerComingResultEnum;
use App\Models\Customer;
use Illuminate\Contracts\Pagination\Paginator;

class CustomerRepository extends BaseRepository implements CustomerRepositoryInterface
{
    public function model(): string
    {
        return Customer::class;
    }

    public function getPaginator(int $perPage, array $conditions = []): Paginator
    {
        return $this->model
            ->newQuery()
            ->when(isset($conditions['keyword']) && strlen($conditions['keyword']), function ($builder) use ($conditions) {
                $builder->where(function ($builder) use ($conditions) {
                    $builder
                        ->orWhere('name', 'LIKE', "%{$conditions['keyword']}%")
                        ->orWhere('phone_number', 'LIKE', "%{$conditions['keyword']}%");
                });
            })
            ->when(!empty($conditions['has_revenue']), function ($builder) {
                $builder->whereHas('customerRevenues');
            })
            ->with([
                'firstNewBooking',
                'firstNewBooking.product',
                'firstNewBooking.bookingSources',
                'firstNewBooking.facebookPage',
                'customerRevenues',
            ])
            ->withCount([
                'bookings' => function ($builder) {
                    $builder->whereIn('customer_coming_result', CustomerComingResultEnum::getCameResults());
                },
            ])
            ->withCount([
                'bookings as bookings_failed_count' => function ($builder) {
                    $builder->where('customer_coming_result', CustomerComingResultEnum::CameWithFailed);
                },
            ])
            ->paginate($perPage);
    }

    public function findByPhoneNumber(string $phoneNumber): ?Customer
    {
        // Sử dụng method exact match từ model
        return Customer::findByExactPhoneNumber($phoneNumber);
    }

    public function existsCustomerByPhoneNumber(string $phoneNumber, $exceptId = null): bool
    {
        // Sử dụng method exact match từ model
        return Customer::existsByExactPhoneNumber($phoneNumber, $exceptId);
    }
}
