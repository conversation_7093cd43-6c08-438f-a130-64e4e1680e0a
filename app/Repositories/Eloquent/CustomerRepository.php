<?php

namespace App\Repositories\Eloquent;

use App\Contracts\Repositories\CustomerRepositoryInterface;
use App\Enums\Booking\CustomerComingResultEnum;
use App\Models\Customer;
use Illuminate\Contracts\Pagination\Paginator;

class CustomerRepository extends BaseRepository implements CustomerRepositoryInterface
{
    public function model(): string
    {
        return Customer::class;
    }

    public function getPaginator(int $perPage, array $conditions = []): Paginator
    {
        return $this->model
            ->newQuery()
            ->when(isset($conditions['keyword']) && strlen($conditions['keyword']), function ($builder) use ($conditions) {
                $builder->where(function ($builder) use ($conditions) {
                    $builder
                        ->orWhere('name', 'LIKE', "%{$conditions['keyword']}%")
                        ->orWhere('phone_number', 'LIKE', "%{$conditions['keyword']}%");
                });
            })
            ->when(!empty($conditions['has_revenue']), function ($builder) {
                $builder->whereHas('customerRevenues');
            })
            ->with([
                'firstNewBooking',
                'firstNewBooking.product',
                'firstNewBooking.bookingSources',
                'firstNewBooking.facebookPage',
                'customerRevenues',
            ])
            ->withCount([
                'bookings' => function ($builder) {
                    $builder->whereIn('customer_coming_result', CustomerComingResultEnum::getCameResults());
                },
            ])
            ->withCount([
                'bookings as bookings_failed_count' => function ($builder) {
                    $builder->where('customer_coming_result', CustomerComingResultEnum::CameWithFailed);
                },
            ])
            ->paginate($perPage);
    }

    public function findByPhoneNumber(string $phoneNumber): ?Customer
    {
        // Normalize phone number để tìm chính xác
        $normalizedPhoneNumber = $this->normalizePhoneNumber($phoneNumber);

        return $this->model
            ->newQuery()
            ->where('phone_number', $normalizedPhoneNumber)
            ->first();
    }

    public function existsCustomerByPhoneNumber(string $phoneNumber, $exceptId = null): bool
    {
        // Normalize phone number để check chính xác
        $normalizedPhoneNumber = $this->normalizePhoneNumber($phoneNumber);

        return $this
            ->model
            ->newQuery()
            ->when(isset($exceptId), function ($builder) use ($exceptId) {
                $builder->whereNotIn('id', (array)$exceptId);
            })
            ->where('phone_number', $normalizedPhoneNumber)
            ->exists();
    }

    /**
     * Normalize phone number để tránh duplicate với leading zeros
     * VD: 00923 -> 0923, 000923 -> 0923
     */
    protected function normalizePhoneNumber(string $phoneNumber): string
    {
        // Loại bỏ tất cả ký tự không phải số
        $phoneNumber = preg_replace('/[^0-9]/', '', $phoneNumber);

        // Loại bỏ leading zeros, nhưng giữ lại ít nhất 1 số 0 đầu cho số VN
        // VD: 00923 -> 0923, 000923 -> 0923, 0923 -> 0923
        $phoneNumber = ltrim($phoneNumber, '0');

        // Nếu sau khi loại bỏ leading zeros mà rỗng hoặc không bắt đầu bằng số phù hợp
        // thì thêm lại 1 số 0 đầu (cho số điện thoại VN)
        if (empty($phoneNumber) || !in_array(substr($phoneNumber, 0, 1), ['3', '5', '7', '8', '9'])) {
            $phoneNumber = '0' . $phoneNumber;
        } else {
            // Thêm 0 đầu cho số điện thoại VN chuẩn
            $phoneNumber = '0' . $phoneNumber;
        }

        return $phoneNumber;
    }
}
