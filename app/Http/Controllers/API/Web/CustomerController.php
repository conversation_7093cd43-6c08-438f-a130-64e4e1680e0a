<?php

namespace App\Http\Controllers\API\Web;

use App\Contracts\Repositories\CustomerRepositoryInterface;
use App\Http\Controllers\API\APIController;
use App\Http\Resources\WebAPI\Customer\CustomerRevenueResource;
use App\Models\Booking;
use App\Models\Customer;
use App\Services\CustomerRevenue\CustomerRevenueService;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;

class CustomerController extends APIController
{
    protected CustomerRepositoryInterface $customerRepository;
    protected CustomerRevenueService $customerRevenueService;

    public function __construct(
        CustomerRepositoryInterface $customerRepository,
        CustomerRevenueService      $customerRevenueService
    )
    {
        $this->customerRepository = $customerRepository;
        $this->customerRevenueService = $customerRevenueService;
    }

    public function showByPhoneNumber(Request $request, $action = null): JsonResponse
    {
        if (!$customer = $this->customerRepository->findByPhoneNumber($request->input('phone_number'))) {
            abort(404);
        }

        if ($action == 'create-booking') {
            // Không check permission cho auto-fill vì chỉ là hiển thị thông tin cơ bản
            // User đã được authenticate để vào trang booking create/edit

            // Trả về thông tin customer cơ bản để auto-fill form
            return $this->responseJsonSuccess([
                'id' => $customer->id,
                'name' => $customer->name,
                'age' => $customer->age,
                'province_id' => $customer->province_id,
                'address' => $customer->address,
                'phone_number' => $customer->phone_number,
            ]);
        } else {
            // Đối với các action khác, vẫn check quyền viewCustomerRevenues
            $this->authorize('viewCustomerRevenues', $customer);
            return $this->responseJsonSuccess($customer);
        }
    }

    public function showCustomerRevenues(Customer $customer)
    {
        $this->authorize('viewCustomerRevenues', $customer);

        $revenues = $this->customerRevenueService->getRevenueGroupByDateByCustomerId($customer->getKey());

        return $this->responseJsonSuccess(
            CustomerRevenueResource::collection($revenues)
        );
    }

    public function showCustomerDebt(Customer $customer)
    {
        $this->authorize('viewCustomerRevenues', $customer);

        $debt = $this->customerRevenueService->getCustomerDebt($customer->getKey());

        return $this->responseJsonSuccess($debt);
    }
}
