<?php

namespace App\Services\Booking;

use App\Contracts\Repositories\CustomerRepositoryInterface;
use App\Models\Booking;
use App\Models\Customer;
use App\Models\User;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class BookingCustomerService
{
    protected CustomerRepositoryInterface $customerRepository;

    public function __construct(CustomerRepositoryInterface $customerRepository)
    {
        $this->customerRepository = $customerRepository;
    }

    /**
     * Xử lý customer cho booking - strategy pattern dựa trên context
     */
    public function handleCustomerForBooking(
        array $customerAttributes,
        ?Booking $booking = null,
        User $user = null
    ): Customer {
        return DB::transaction(function () use ($customerAttributes, $booking, $user) {
            // Chỉ làm sạch phone number (loại bỏ ký tự đặc biệt) nhưng giữ nguyên leading zeros
            $phoneNumber = $this->cleanPhoneNumber($customerAttributes['phone_number']);
            $customerAttributes['phone_number'] = $phoneNumber;

            $existingCustomer = $this->customerRepository->findByPhoneNumber($phoneNumber);

            // Case 1: Tạo booking mới
            if (!$booking) {
                return $this->handleNewBookingCustomer($customerAttributes, $existingCustomer);
            }

            // Case 2: Edit booking hiện tại
            return $this->handleEditBookingCustomer($customerAttributes, $booking, $existingCustomer, $user);
        });
    }

    /**
     * Xử lý customer khi tạo booking mới
     */
    protected function handleNewBookingCustomer(array $customerAttributes, ?Customer $existingCustomer): Customer
    {
        if ($existingCustomer) {
            // Customer đã tồn tại - có thể update thông tin nếu cần
            return $this->updateCustomerIfNeeded($existingCustomer, $customerAttributes);
        }

        // Tạo customer mới
        return $this->customerRepository->create($customerAttributes);
    }

    /**
     * Xử lý customer khi edit booking
     */
    protected function handleEditBookingCustomer(
        array $customerAttributes,
        Booking $booking,
        ?Customer $existingCustomer,
        ?User $user
    ): Customer {
        $currentCustomer = $booking->customer;
        $phoneNumber = $customerAttributes['phone_number'];

        // Case 1: Phone number không thay đổi
        if ($currentCustomer->phone_number === $phoneNumber) {
            return $this->updateCustomerIfAllowed($currentCustomer, $customerAttributes, $user);
        }

        // Case 2: Phone number thay đổi
        return $this->handlePhoneNumberChange($customerAttributes, $currentCustomer, $existingCustomer, $user);
    }

    /**
     * Xử lý khi phone number thay đổi
     */
    protected function handlePhoneNumberChange(
        array $customerAttributes,
        Customer $currentCustomer,
        ?Customer $existingCustomer,
        ?User $user
    ): Customer {
        if ($existingCustomer) {
            // Phone number mới đã thuộc về customer khác
            // Strategy: Chuyển booking sang customer đã tồn tại
            // Update thông tin customer nếu cần thiết
            return $this->updateCustomerIfNeeded($existingCustomer, $customerAttributes);
        }

        // Phone number mới chưa tồn tại
        // Strategy: Update customer hiện tại với phone number mới
        if ($this->canUpdateCustomerPhoneNumber($currentCustomer, $user)) {
            $currentCustomer->update($customerAttributes);
            return $currentCustomer->refresh();
        }

        // Không có quyền update - giữ nguyên customer hiện tại
        return $currentCustomer;
    }

    /**
     * Update customer nếu thông tin thay đổi và có quyền
     */
    protected function updateCustomerIfAllowed(Customer $customer, array $attributes, ?User $user): Customer
    {
        // Kiểm tra quyền update customer
        if (!$user || !$user->can('update', $customer)) {
            return $customer; // Không có quyền - giữ nguyên
        }

        // Chỉ update những field được phép
        $allowedFields = $this->getAllowedUpdateFields($user);
        $updateData = array_intersect_key($attributes, array_flip($allowedFields));

        if (!empty($updateData)) {
            $customer->update($updateData);
            return $customer->refresh();
        }

        return $customer;
    }

    /**
     * Update customer nếu thông tin mới khác với hiện tại và hợp lý
     */
    protected function updateCustomerIfNeeded(Customer $customer, array $attributes): Customer
    {
        $needsUpdate = false;
        $updateData = [];

        // Chỉ update những field không quan trọng và có thể thay đổi theo thời gian
        foreach (['name', 'age', 'address', 'province_id'] as $field) {
            if (isset($attributes[$field]) && $attributes[$field] !== null && $attributes[$field] !== '') {
                // Chỉ update nếu thông tin mới có vẻ hợp lý hơn
                if ($this->shouldUpdateField($customer, $field, $attributes[$field])) {
                    $updateData[$field] = $attributes[$field];
                    $needsUpdate = true;
                }
            }
        }

        if ($needsUpdate) {
            $customer->update($updateData);
            return $customer->refresh();
        }

        return $customer;
    }

    /**
     * Kiểm tra có nên update field này không
     */
    protected function shouldUpdateField(Customer $customer, string $field, $newValue): bool
    {
        $currentValue = $customer->{$field};

        // Nếu field hiện tại rỗng, luôn update
        if (empty($currentValue)) {
            return true;
        }

        // Nếu giá trị mới rỗng, không update
        if (empty($newValue)) {
            return false;
        }

        // Đối với age, chỉ update nếu tuổi mới lớn hơn (người ta lớn lên theo thời gian)
        if ($field === 'age') {
            return $newValue > $currentValue;
        }

        // Đối với name và address, chỉ update nếu thông tin mới dài hơn (chi tiết hơn)
        if (in_array($field, ['name', 'address'])) {
            return strlen($newValue) > strlen($currentValue);
        }

        // Các field khác, update nếu khác nhau
        return $currentValue !== $newValue;
    }

    /**
     * Kiểm tra quyền update phone number
     */
    protected function canUpdateCustomerPhoneNumber(Customer $customer, ?User $user): bool
    {
        if (!$user) {
            return false;
        }

        return $user->can('updatePhoneNumber', $customer);
    }

    /**
     * Lấy danh sách field được phép update
     */
    protected function getAllowedUpdateFields(?User $user): array
    {
        $baseFields = ['name', 'age', 'address', 'province_id'];

        // Tạo một Customer instance tạm thời để check policy
        // Vì policy updatePhoneNumber yêu cầu Customer instance, không phải class
        if ($user) {
            $tempCustomer = new Customer();
            if ($user->can('updatePhoneNumber', $tempCustomer)) {
                $baseFields[] = 'phone_number';
            }
        }

        return $baseFields;
    }

    /**
     * Làm sạch phone number - chỉ loại bỏ ký tự đặc biệt, giữ nguyên leading zeros
     * VD: "0824-083-812" -> "0824083812", "00824083812" -> "00824083812"
     */
    protected function cleanPhoneNumber(string $phoneNumber): string
    {
        // Chỉ loại bỏ ký tự không phải số, giữ nguyên tất cả số 0 đầu
        return preg_replace('/[^0-9]/', '', $phoneNumber);
    }

    /**
     * Log customer changes để track changes (sử dụng Laravel Log)
     */
    public function logCustomerChange(
        Customer $customer,
        array $oldData,
        array $newData,
        User $user,
        string $context,
        ?Booking $booking = null
    ): void {
        $changedFields = [];
        $changes = [];

        // Tìm các field thay đổi
        foreach ($newData as $field => $newValue) {
            $oldValue = $oldData[$field] ?? null;
            if ($oldValue !== $newValue) {
                $changedFields[] = $field;
                $changes[$field] = [
                    'old' => $oldValue,
                    'new' => $newValue
                ];
            }
        }

        // Chỉ log nếu có thay đổi
        if (!empty($changedFields)) {
            Log::info('Customer data changed', [
                'customer_id' => $customer->id,
                'customer_phone' => $customer->phone_number,
                'booking_id' => $booking?->id,
                'user_id' => $user->id,
                'user_name' => $user->name,
                'context' => $context,
                'changed_fields' => $changedFields,
                'changes' => $changes,
                'timestamp' => now()->toISOString(),
            ]);
        }
    }
}
