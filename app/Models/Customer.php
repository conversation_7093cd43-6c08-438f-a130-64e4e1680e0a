<?php

namespace App\Models;

use App\Enums\Booking\BookingTypeEnum;
use App\Enums\CustomerRevenue\PaymentMethodEnum;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Support\Str;

class Customer extends Model
{
    use HasFactory, SoftDeletes;

    protected $fillable = [
        'phone_number',
        'name',
        'age',
        'province_id',
        'address',
        'card_number',
        'card_amount',
        'is_public_phone',
    ];

    /**
     * Mutator để normalize phone number khi save
     * Tránh duplicate với leading zeros: 00923 -> 0923
     */
    public function setPhoneNumberAttribute($value)
    {
        $this->attributes['phone_number'] = $this->normalizePhoneNumber($value);
    }

    public function province()
    {
        return $this->belongsTo(Province::class);
    }

    public function customerRevenues()
    {
        return $this->hasMany(CustomerRevenue::class);
    }

    public function firstTimeCustomerRevenues()
    {
        return $this->hasMany(CustomerRevenue::class)
            ->where('first_time_flag', true);
    }

    public function bookings()
    {
        return $this->hasMany(Booking::class);
    }

    public function booking()
    {
        return $this->hasOne(Booking::class);
    }

    public function firstNewBooking()
    {
        return $this->booking()->where('type', BookingTypeEnum::New);
    }

    public function getPhoneNumberByUser(User $user, bool $isCustomPermission = false, string $mask = '*')
    {
        if($user->isTPMKT() || $user->isSuperViewer()) {
            return $this->phone_number;
        }

        if (!$this->is_public_phone && !$user->isAdministrator() && !$user->isSalesman() && !$user->isAccountantBO()) {
            return '-';
        }

        if ($user->can('viewPhoneNumber', $this) || $isCustomPermission) {
            return $this->phone_number;
        }

        if (!$length = strlen($this->phone_number)) {
            return $this->phone_number;
        }

        return Str::mask(
            $this->phone_number,
            $mask,
            0,
            $length - config('common.display_phone_number_digits_count')
        );
    }

    public function sumRevenueOfFirstTime($default = null): ?float
    {
        $revenue = $this->firstTimeCustomerRevenues()->sum('revenue');

        return $revenue ?: $default;
    }

    public function sumRevenue(): ?float
    {
        return $this->customerRevenues()->sum('revenue');
    }

    public function sumDebt(): ?float
    {
        return $this->customerRevenues()->sum('debt');
    }

    public function bookingCount(): ?int
    {
        return $this->bookings()->count();
    }

    public function getCurrentMonthlyPaymentAmount(): float
    {
        $customerRevenue = $this->customerRevenues()
            ->whereRelation('customerRevenueDetails', 'payment_method', PaymentMethodEnum::MonthlyPayment)
            ->latest()
            ->first();

        $monthlyPayment = $customerRevenue->monthlyPayment;

        if (!$monthlyPayment) {
            return 0;
        }

        return $monthlyPayment->total_amount - $monthlyPayment->monthlyPaymentDetail->sum('amount');
    }

    /**
     * Normalize phone number để tránh duplicate với leading zeros
     * VD: 00923 -> 0923, 000923 -> 0923
     */
    protected function normalizePhoneNumber(string $phoneNumber): string
    {
        // Loại bỏ tất cả ký tự không phải số
        $phoneNumber = preg_replace('/[^0-9]/', '', $phoneNumber);

        // Loại bỏ leading zeros, nhưng giữ lại ít nhất 1 số 0 đầu cho số VN
        // VD: 00923 -> 0923, 000923 -> 0923, 0923 -> 0923
        $phoneNumber = ltrim($phoneNumber, '0');

        // Nếu sau khi loại bỏ leading zeros mà rỗng hoặc không bắt đầu bằng số phù hợp
        // thì thêm lại 1 số 0 đầu (cho số điện thoại VN)
        if (empty($phoneNumber) || !in_array(substr($phoneNumber, 0, 1), ['3', '5', '7', '8', '9'])) {
            $phoneNumber = '0' . $phoneNumber;
        } else {
            // Thêm 0 đầu cho số điện thoại VN chuẩn
            $phoneNumber = '0' . $phoneNumber;
        }

        return $phoneNumber;
    }
}
