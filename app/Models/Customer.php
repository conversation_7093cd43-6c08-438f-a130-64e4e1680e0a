<?php

namespace App\Models;

use App\Enums\Booking\BookingTypeEnum;
use App\Enums\CustomerRevenue\PaymentMethodEnum;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Support\Str;

class Customer extends Model
{
    use HasFactory, SoftDeletes;

    protected $fillable = [
        'phone_number',
        'name',
        'age',
        'province_id',
        'address',
        'card_number',
        'card_amount',
        'is_public_phone',
    ];

    /**
     * Mutator để làm sạch phone number khi save
     * Chỉ loại bỏ ký tự đặc biệt, giữ nguyên leading zeros
     */
    public function setPhoneNumberAttribute($value)
    {
        $this->attributes['phone_number'] = $this->cleanPhoneNumber($value);
    }

    public function province()
    {
        return $this->belongsTo(Province::class);
    }

    public function customerRevenues()
    {
        return $this->hasMany(CustomerRevenue::class);
    }

    public function firstTimeCustomerRevenues()
    {
        return $this->hasMany(CustomerRevenue::class)
            ->where('first_time_flag', true);
    }

    public function bookings()
    {
        return $this->hasMany(Booking::class);
    }

    public function booking()
    {
        return $this->hasOne(Booking::class);
    }

    public function firstNewBooking()
    {
        return $this->booking()->where('type', BookingTypeEnum::New);
    }

    public function getPhoneNumberByUser(User $user, bool $isCustomPermission = false, string $mask = '*')
    {
        if($user->isTPMKT() || $user->isSuperViewer()) {
            return $this->phone_number;
        }

        if (!$this->is_public_phone && !$user->isAdministrator() && !$user->isSalesman() && !$user->isAccountantBO()) {
            return '-';
        }

        if ($user->can('viewPhoneNumber', $this) || $isCustomPermission) {
            return $this->phone_number;
        }

        if (!$length = strlen($this->phone_number)) {
            return $this->phone_number;
        }

        return Str::mask(
            $this->phone_number,
            $mask,
            0,
            $length - config('common.display_phone_number_digits_count')
        );
    }

    public function sumRevenueOfFirstTime($default = null): ?float
    {
        $revenue = $this->firstTimeCustomerRevenues()->sum('revenue');

        return $revenue ?: $default;
    }

    public function sumRevenue(): ?float
    {
        return $this->customerRevenues()->sum('revenue');
    }

    public function sumDebt(): ?float
    {
        return $this->customerRevenues()->sum('debt');
    }

    public function bookingCount(): ?int
    {
        return $this->bookings()->count();
    }

    public function getCurrentMonthlyPaymentAmount(): float
    {
        $customerRevenue = $this->customerRevenues()
            ->whereRelation('customerRevenueDetails', 'payment_method', PaymentMethodEnum::MonthlyPayment)
            ->latest()
            ->first();

        $monthlyPayment = $customerRevenue->monthlyPayment;

        if (!$monthlyPayment) {
            return 0;
        }

        return $monthlyPayment->total_amount - $monthlyPayment->monthlyPaymentDetail->sum('amount');
    }

    /**
     * Làm sạch phone number - chỉ loại bỏ ký tự đặc biệt, giữ nguyên leading zeros
     * VD: "0824-083-812" -> "0824083812", "00824083812" -> "00824083812"
     */
    protected function cleanPhoneNumber(string $phoneNumber): string
    {
        // Chỉ loại bỏ ký tự không phải số, giữ nguyên tất cả số 0 đầu
        return preg_replace('/[^0-9]/', '', $phoneNumber);
    }
}
